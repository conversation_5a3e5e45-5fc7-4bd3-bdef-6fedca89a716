<template>
  <div class="batch-treasury-payment">
    <!-- 操作按钮行 -->
    <div class="action-buttons">
      <el-button type="primary" @click="getTemplateData" :loading="isLoadingTemplate" icon="Download">
        查询
      </el-button>
      <el-button type="success" @click="calculatePayment" :loading="isCalculating" icon="Calculator">
        保存
      </el-button>
      <el-button type="warning" @click="executePayment" :loading="isExecuting" icon="Check">
        执行批量录入
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <UniversalTableComponent 
        ref="tableRef" 
        :initial-data="tableData" 
        :data-provider="dataProvider"
        workbook-name="批量司库付款单" 
        @data-change="handleDataChange" 
        @error="handleError"
        @initialized="handleTableInitialized" 
      />
    </div>

    <!-- 消息轮询对话框 -->
    <MessagePollingDialog v-model="dialogVisible" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import UniversalTableComponent from './UniversalTableComponent.vue'
import MessagePollingDialog from './MessagePollingDialog.vue'

// 响应式数据
const tableRef = ref(null)
const tableData = ref({})
const dialogVisible = ref(false)

// 加载状态
const isLoadingTemplate = ref(false)
const isCalculating = ref(false)
const isExecuting = ref(false)

// 获取模板数据
const getTemplateData = async () => {
  try {
    isLoadingTemplate.value = true

    const response = await fetch('http://127.0.0.1:8000/api/batch-treasury-payment/template', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 直接赋值，让Vue的响应式系统处理
      tableData.value = result.data

      ElMessage.success('司库付款单模板数据获取成功')
    } else {
      throw new Error(result.message || '获取模板数据失败')
    }
  } catch (error) {
    console.error('获取模板数据失败:', error)
    ElMessage.error('获取模板数据失败: ' + error.message)
  } finally {
    isLoadingTemplate.value = false
  }
}

// 计算付款金额
const calculatePayment = async () => {
  try {
    isCalculating.value = true

    // 获取当前表格数据
    const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    const response = await fetch('http://127.0.0.1:8000/api/batch-treasury-payment/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        currentData: currentData,
        timestamp: new Date().toISOString()
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      // 更新表格数据
      if (result.data) {
        tableData.value = result.data
      }

      ElMessage.success('付款金额计算完成')
    } else {
      throw new Error(result.message || '付款金额计算失败')
    }
  } catch (error) {
    console.error('付款金额计算失败:', error)
    ElMessage.error('付款金额计算失败: ' + error.message)
  } finally {
    isCalculating.value = false
  }
}

// 执行批量付款
const executePayment = async () => {
  try {
    isExecuting.value = true

    // 获取当前表格数据
    const currentData = tableRef.value ? tableRef.value.getAllTableData() : {}

    // 验证数据
    if (!currentData || Object.keys(currentData).length === 0) {
      ElMessage.warning('请先获取模板数据')
      return
    }

    const response = await fetch('http://127.0.0.1:8000/api/start-function', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        '功能': '执行支付提单',
        '参数': []
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (response.ok) {
      // 显示消息轮询对话框
      dialogVisible.value = true
      ElMessage.success('批量司库付款任务已启动')
    } else {
      throw new Error(result.message || '批量司库付款启动失败')
    }
  } catch (error) {
    console.error('批量司库付款失败:', error)
    ElMessage.error('批量司库付款失败: ' + error.message)
  } finally {
    isExecuting.value = false
  }
}

// 数据提供函数（用于刷新）
const dataProvider = async () => {
  const response = await fetch('http://127.0.0.1:8000/api/batch-treasury-payment/template', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const result = await response.json()

  if (result.code === 200) {
    return result.data
  } else {
    throw new Error(result.message || '获取数据失败')
  }
}

// 事件处理
const handleDataChange = (data) => {
  console.log('批量司库付款表格数据变化:', data)
}

const handleError = (error) => {
  console.error('批量司库付款表格错误:', error)
  ElMessage.error(error)
}

const handleTableInitialized = () => {
  console.log('批量司库付款表格初始化完成')
}

// 组件挂载时的初始化
onMounted(() => {
  getTemplateData()
  console.log('批量司库付款组件已挂载')
})
</script>

<style scoped>
.batch-treasury-payment {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: #f5f7fa;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-container {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.el-button {
  height: 36px;
  padding: 0 20px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-button.is-loading {
  transform: none;
}
</style>
